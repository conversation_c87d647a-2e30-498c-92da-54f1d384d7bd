import random


class CPFGenerator:
    """Gerador de CPFs válidos que começam com '01'."""
    
    def __init__(self):
        pass
    
    def _calculate_digit(self, cpf_digits, weights):
        """Calcula um dígito verificador do CPF."""
        total = sum(int(digit) * weight for digit, weight in zip(cpf_digits, weights))
        remainder = total % 11
        return 0 if remainder < 2 else 11 - remainder
    
    def _generate_cpf_digits(self):
        """Gera os 9 primeiros dígitos do CPF começando com '01'."""
        # Os dois primeiros dígitos são fixos: '01'
        digits = ['0', '1']
        
        # Gera os próximos 7 dígitos aleatoriamente
        for _ in range(7):
            digits.append(str(random.randint(0, 9)))
        
        return digits
    
    def _format_cpf(self, cpf_digits):
        """Formata o CPF no padrão XXX.XXX.XXX-XX."""
        cpf_str = ''.join(cpf_digits)
        return f"{cpf_str[:3]}.{cpf_str[3:6]}.{cpf_str[6:9]}-{cpf_str[9:]}"
    
    def generate_single_cpf(self):
        """Gera um único CPF válido começando com '01'."""
        # Gera os 9 primeiros dígitos
        cpf_digits = self._generate_cpf_digits()
        
        # Calcula o primeiro dígito verificador
        weights_first = [10, 9, 8, 7, 6, 5, 4, 3, 2]
        first_digit = self._calculate_digit(cpf_digits, weights_first)
        cpf_digits.append(str(first_digit))
        
        # Calcula o segundo dígito verificador
        weights_second = [11, 10, 9, 8, 7, 6, 5, 4, 3, 2]
        second_digit = self._calculate_digit(cpf_digits, weights_second)
        cpf_digits.append(str(second_digit))
        
        return self._format_cpf(cpf_digits)
    
    def generate_cpfs(self, quantity=10):
        """
        Gera uma lista de CPFs válidos começando com '01'.
        
        Args:
            quantity (int): Quantidade de CPFs a serem gerados (padrão: 10)
        
        Returns:
            list: Lista com os CPFs gerados
        """
        cpfs = []
        for _ in range(quantity):
            cpfs.append(self.generate_single_cpf())
        return cpfs
    
    def validate_cpf(self, cpf):
        """
        Valida se um CPF é válido.
        
        Args:
            cpf (str): CPF a ser validado (pode estar formatado ou não)
        
        Returns:
            bool: True se o CPF for válido, False caso contrário
        """
        # Remove formatação
        cpf_clean = ''.join(filter(str.isdigit, cpf))
        
        # Verifica se tem 11 dígitos
        if len(cpf_clean) != 11:
            return False
        
        # Verifica se não são todos os dígitos iguais
        if cpf_clean == cpf_clean[0] * 11:
            return False
        
        # Valida os dígitos verificadores
        cpf_digits = list(cpf_clean[:9])
        
        # Primeiro dígito verificador
        weights_first = [10, 9, 8, 7, 6, 5, 4, 3, 2]
        first_digit = self._calculate_digit(cpf_digits, weights_first)
        
        if int(cpf_clean[9]) != first_digit:
            return False
        
        # Segundo dígito verificador
        cpf_digits.append(str(first_digit))
        weights_second = [11, 10, 9, 8, 7, 6, 5, 4, 3, 2]
        second_digit = self._calculate_digit(cpf_digits, weights_second)
        
        return int(cpf_clean[10]) == second_digit


def main():
    """Função principal para demonstrar o uso do gerador."""
    generator = CPFGenerator()
    
    print("Gerando 10 CPFs que começam com '01':")
    print("-" * 40)
    
    cpfs = generator.generate_cpfs(10)
    for i, cpf in enumerate(cpfs, 1):
        print(f"{i:2d}. {cpf}")
    
    print("\nValidando os CPFs gerados:")
    print("-" * 30)
    
    for cpf in cpfs:
        is_valid = generator.validate_cpf(cpf)
        status = "✓ Válido" if is_valid else "✗ Inválido"
        print(f"{cpf} - {status}")


if __name__ == "__main__":
    main()
