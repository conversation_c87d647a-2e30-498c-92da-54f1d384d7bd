import unittest
import re
from cpf_generator import CPFGenerator


class TestCPFGenerator(unittest.TestCase):
    """Testes unitários para a classe CPFGenerator."""
    
    def setUp(self):
        """Configuração inicial para cada teste."""
        self.generator = CPFGenerator()
    
    def test_generate_single_cpf_format(self):
        """Testa se o CPF gerado tem o formato correto."""
        cpf = self.generator.generate_single_cpf()
        
        # Verifica se o formato está correto (XXX.XXX.XXX-XX)
        pattern = r'^\d{3}\.\d{3}\.\d{3}-\d{2}$'
        self.assertTrue(re.match(pattern, cpf), 
                       f"CPF {cpf} não está no formato correto")
    
    def test_generate_single_cpf_starts_with_01(self):
        """Testa se o CPF gerado começa com '01'."""
        cpf = self.generator.generate_single_cpf()
        
        # Remove formatação para verificar os primeiros dígitos
        cpf_clean = cpf.replace('.', '').replace('-', '')
        self.assertTrue(cpf_clean.startswith('01'), 
                       f"CPF {cpf} não começa com '01'")
    
    def test_generate_single_cpf_is_valid(self):
        """Testa se o CPF gerado é válido."""
        cpf = self.generator.generate_single_cpf()
        
        # Verifica se o CPF gerado é válido usando o próprio validador
        self.assertTrue(self.generator.validate_cpf(cpf), 
                       f"CPF {cpf} não é válido")
    
    def test_generate_cpfs_default_quantity(self):
        """Testa se a função gera 10 CPFs por padrão."""
        cpfs = self.generator.generate_cpfs()
        
        self.assertEqual(len(cpfs), 10, 
                        "Deveria gerar 10 CPFs por padrão")
    
    def test_generate_cpfs_custom_quantity(self):
        """Testa se a função gera a quantidade especificada de CPFs."""
        quantities = [1, 5, 15, 20]
        
        for qty in quantities:
            with self.subTest(quantity=qty):
                cpfs = self.generator.generate_cpfs(qty)
                self.assertEqual(len(cpfs), qty, 
                               f"Deveria gerar {qty} CPFs")
    
    def test_generate_cpfs_all_start_with_01(self):
        """Testa se todos os CPFs gerados começam com '01'."""
        cpfs = self.generator.generate_cpfs(10)
        
        for cpf in cpfs:
            with self.subTest(cpf=cpf):
                cpf_clean = cpf.replace('.', '').replace('-', '')
                self.assertTrue(cpf_clean.startswith('01'), 
                               f"CPF {cpf} não começa com '01'")
    
    def test_generate_cpfs_all_valid(self):
        """Testa se todos os CPFs gerados são válidos."""
        cpfs = self.generator.generate_cpfs(10)
        
        for cpf in cpfs:
            with self.subTest(cpf=cpf):
                self.assertTrue(self.generator.validate_cpf(cpf), 
                               f"CPF {cpf} não é válido")
    
    def test_generate_cpfs_uniqueness(self):
        """Testa se os CPFs gerados são únicos (probabilisticamente)."""
        # Gera uma quantidade maior para aumentar a chance de detectar duplicatas
        cpfs = self.generator.generate_cpfs(50)
        
        # Verifica se não há duplicatas
        unique_cpfs = set(cpfs)
        self.assertEqual(len(cpfs), len(unique_cpfs), 
                        "Foram encontrados CPFs duplicados")
    
    def test_validate_cpf_valid_cases(self):
        """Testa a validação com CPFs válidos conhecidos."""
        # CPFs válidos que começam com 01 (calculados manualmente)
        valid_cpfs = [
            "01234567890",  # Sem formatação
            "012.345.678-90",  # Com formatação
        ]
        
        # Gera alguns CPFs válidos para testar
        generated_cpfs = self.generator.generate_cpfs(5)
        valid_cpfs.extend(generated_cpfs)
        
        for cpf in valid_cpfs:
            with self.subTest(cpf=cpf):
                self.assertTrue(self.generator.validate_cpf(cpf), 
                               f"CPF {cpf} deveria ser válido")
    
    def test_validate_cpf_invalid_cases(self):
        """Testa a validação com CPFs inválidos."""
        invalid_cpfs = [
            "00000000000",  # Todos os dígitos iguais
            "11111111111",  # Todos os dígitos iguais
            "123456789",    # Menos de 11 dígitos
            "12345678901234",  # Mais de 11 dígitos
            "012.345.678-99",  # Dígito verificador incorreto
            "abc.def.ghi-jk",  # Caracteres não numéricos
            "",  # String vazia
        ]
        
        for cpf in invalid_cpfs:
            with self.subTest(cpf=cpf):
                self.assertFalse(self.generator.validate_cpf(cpf), 
                                f"CPF {cpf} deveria ser inválido")
    
    def test_calculate_digit_method(self):
        """Testa o método interno de cálculo de dígito verificador."""
        # Teste com valores conhecidos
        cpf_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8']
        weights = [10, 9, 8, 7, 6, 5, 4, 3, 2]
        
        # Calcula o dígito manualmente para verificar
        expected_total = sum(int(d) * w for d, w in zip(cpf_digits, weights))
        expected_remainder = expected_total % 11
        expected_digit = 0 if expected_remainder < 2 else 11 - expected_remainder
        
        calculated_digit = self.generator._calculate_digit(cpf_digits, weights)
        
        self.assertEqual(calculated_digit, expected_digit, 
                        "Cálculo do dígito verificador incorreto")
    
    def test_format_cpf_method(self):
        """Testa o método interno de formatação do CPF."""
        cpf_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
        expected_format = "012.345.678-90"
        
        formatted_cpf = self.generator._format_cpf(cpf_digits)
        
        self.assertEqual(formatted_cpf, expected_format, 
                        "Formatação do CPF incorreta")


class TestCPFGeneratorIntegration(unittest.TestCase):
    """Testes de integração para verificar o funcionamento completo."""
    
    def setUp(self):
        """Configuração inicial para cada teste."""
        self.generator = CPFGenerator()
    
    def test_full_workflow(self):
        """Testa o fluxo completo: gerar CPFs e validá-los."""
        # Gera CPFs
        cpfs = self.generator.generate_cpfs(10)
        
        # Verifica se todos foram gerados
        self.assertEqual(len(cpfs), 10)
        
        # Verifica se todos são válidos
        for cpf in cpfs:
            self.assertTrue(self.generator.validate_cpf(cpf))
            
            # Verifica se começam com 01
            cpf_clean = cpf.replace('.', '').replace('-', '')
            self.assertTrue(cpf_clean.startswith('01'))
    
    def test_stress_generation(self):
        """Teste de stress: gera muitos CPFs para verificar consistência."""
        large_quantity = 100
        cpfs = self.generator.generate_cpfs(large_quantity)
        
        # Verifica quantidade
        self.assertEqual(len(cpfs), large_quantity)
        
        # Verifica se todos são válidos
        invalid_count = 0
        for cpf in cpfs:
            if not self.generator.validate_cpf(cpf):
                invalid_count += 1
        
        self.assertEqual(invalid_count, 0, 
                        f"Encontrados {invalid_count} CPFs inválidos em {large_quantity}")


if __name__ == '__main__':
    # Configuração para executar os testes com mais detalhes
    unittest.main(verbosity=2)
